import * as SecureStore from 'expo-secure-store';
import axiosInstance from './config';

export class FileService {
  static async uploadImage(imageFile: File, email: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const formData = new FormData();
      formData.append('imageFile', imageFile);
      formData.append('email', email);

      const response = await axiosInstance.post('/file/v1/upload-image', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = response.data;
      return data;
    } catch (error) {
      throw error;
    }
  }

  static async uploadManyImages(imageFiles: File[], email: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const formData = new FormData();
      imageFiles.forEach((file) => formData.append('imageFiles', file));
      formData.append('email', email);

      const response = await axiosInstance.post('/file/v1/upload-many-images', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = response.data;
      return data;
    } catch (error) {
      throw error;
    }
  }
}
