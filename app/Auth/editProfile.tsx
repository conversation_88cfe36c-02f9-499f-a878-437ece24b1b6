import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { useFocusEffect, useNavigation, useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useRef, useState, useMemo, lazy, Suspense, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  InteractionManager,
} from 'react-native';
import { Toast } from 'toastify-react-native';

import type { BottomSheetHandle } from '~/components/Auth/EditProfileBottomSheet';
import type { ChangePasswordBottomSheetHandle } from '~/components/Auth/ChangePasswordBottomSheet';
import type { GenderSelectBottomSheetHandle } from '~/components/Auth/GenderSelectBottomSheet';
import type { MultiSelectBottomSheetHandle } from '~/components/Auth/MultiSelectBottomSheet';
import type { PhotoBottomSheetHandle } from '~/components/Auth/PhotoUploadBottomSheet';
import { useColorScheme } from '~/lib/useColorScheme';
import { AuthService } from '~/services/AuthService';
import { UserService } from '~/services/UserService';
import { UserStore } from '~/store/store';

// Lazy load heavy bottom sheet components
const EditProfileBottomSheet = lazy(() => import('~/components/Auth/EditProfileBottomSheet'));
const ChangePasswordBottomSheet = lazy(() => import('~/components/Auth/ChangePasswordBottomSheet'));
const PhotoUploadBottomSheet = lazy(() => import('~/components/Auth/PhotoUploadBottomSheet'));
const MultiSelectBottomSheet = lazy(() => import('~/components/Auth/MultiSelectBottomSheet'));
const GenderSelectBottomSheet = lazy(() => import('~/components/Auth/GenderSelectBottomSheet'));

// Available options for interests and event preferences
const allInterests = [
  'technology',
  'music',
  'art',
  'photography',
  'travel',
  'food',
  'fashion',
  'sports',
  'fitness',
  'books',
  'movies',
  'gaming',
  'nature',
  'science',
  'history',
  'languages',
  'dance',
  'theater',
  'business',
  'entrepreneurship',
];

const allEventPreferences = ['Leisure', 'Entertainment', 'Education', 'Business'];

export default function EditProfileScreen() {
  const navigation = useNavigation();
  const router = useRouter();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Optimize user store access with selector and memoization
  const user = UserStore((state: any) => state.user);
  const initialUserData = useMemo(
    () => ({
      id: user.id,
      fullName: user.fullName,
      username: user.username,
      email: user.email,
      phoneNumber: user.phoneNumber,
      gender: user.gender,
      bio: user.bio,
      profileImage: user.profileImage,
      interests: user.interests || [],
      eventPreferences: user.eventPreferences || [],
      upFor: user.upFor,
      visibility: user.visibility,
    }),
    [user]
  );

  // Use lazy initialization to avoid copying large objects immediately
  const [userData, setUserData] = useState(() => ({ ...initialUserData }));
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailChanged, setEmailChanged] = useState(false);
  const [phoneChanged, setPhoneChanged] = useState(false);
  const [isPasswordLoading, setIsPasswordLoading] = useState(false);
  const [showHeavyUI, setShowHeavyUI] = useState(false);

  // Refs for bottom sheets
  const nameSheetRef = useRef<BottomSheetHandle>(null);
  const usernameSheetRef = useRef<BottomSheetHandle>(null);
  const emailSheetRef = useRef<BottomSheetHandle>(null);
  const phoneSheetRef = useRef<BottomSheetHandle>(null);
  const passwordSheetRef = useRef<ChangePasswordBottomSheetHandle>(null);
  const bioSheetRef = useRef<BottomSheetHandle>(null);
  const photoSheetRef = useRef<PhotoBottomSheetHandle>(null);
  const interestsSheetRef = useRef<MultiSelectBottomSheetHandle>(null);
  const eventPrefsSheetRef = useRef<MultiSelectBottomSheetHandle>(null);
  const genderSheetRef = useRef<GenderSelectBottomSheetHandle>(null);

  useFocusEffect(
    useCallback(() => {
      setShowHeavyUI(true);

      return () => {
        InteractionManager.runAfterInteractions(() => {
          setShowHeavyUI(false);
        });
      };
    }, [])
  );

  useEffect(() => {
    console.log('Mounting Screen');
    return () => {
      console.time('Unmounting');
      console.timeEnd('Unmounting');
    };
  }, []);

  // Handler functions
  const handleFullNameSave = (value: string) => {
    handleSubmit({ ...userData, fullName: value });
    setUserData({ ...userData, fullName: value });
  };

  const handleUsernameSave = (value: string) => {
    handleSubmit({ ...userData, username: value });
    setUserData({ ...userData, username: value });
  };

  const sendEmailOtp = async (email: string) => {
    try {
      const response = await AuthService.sendOtp(email);

      Toast.show({
        type: 'success',
        text1: 'OTP Sent',
        text2: 'A verification code has been sent to your email address.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
      return response;
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to send OTP. Please try again.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      Keyboard.dismiss();
    }
  };

  const verifyEmailOtp = async (email: string, otp: string) => {
    try {
      await AuthService.verifyEmail(otp, userData.id);
      Toast.show({
        type: 'success',
        text1: 'Verified',
        text2: 'Your email has been verified.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
      await handleSubmit({ ...userData, email: email });
      Toast.show({
        type: 'info',
        text1: 'Security Notice',
        text2: 'For security, you will be logged out. Please log in with your new email address.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
      setEmailChanged(false);
      AuthService.logout();
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to verify OTP. Please try again.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    }
  };

  const handleEmailSave = async (value: string) => {
    setUserData({ ...userData, email: value });
    setEmailChanged(true);

    try {
      // Send OTP to verify email change
      const response = await sendEmailOtp(value);

      const currentUserID = response.body.userId; // Store the userID from response

      // Prompt user to enter OTP
      Alert.prompt(
        'Verify Email Change',
        'Enter the OTP sent to your email address.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => {
              // Reset email change state if cancelled
              setUserData({ ...userData, email: initialUserData.email });
              setEmailChanged(false);
            },
          },
          {
            text: 'Verify',
            onPress: async (otp) => {
              if (otp) {
                try {
                  // Verify OTP
                  await AuthService.verifyEmail(otp, currentUserID);

                  Toast.show({
                    type: 'success',
                    text1: 'Verified',
                    text2: 'Your email has been verified.',
                    position: 'bottom',
                    theme: isDark ? 'dark' : 'light',
                    backgroundColor: colors.background,
                    autoHide: true,
                  });

                  // Update email after OTP verification
                  await handleSubmit({ ...userData, email: value });

                  Toast.show({
                    type: 'info',
                    text1: 'Security Notice',
                    text2:
                      'For security, you will be logged out. Please log in with your new email address.',
                    position: 'bottom',
                    theme: isDark ? 'dark' : 'light',
                    backgroundColor: colors.background,
                    autoHide: true,
                  });

                  setEmailChanged(false);
                  AuthService.logout();
                } catch (error) {
                  console.log(error);
                  Toast.show({
                    type: 'error',
                    text1: 'Error',
                    text2: 'Failed to verify OTP. Please try again.',
                    position: 'bottom',
                    theme: isDark ? 'dark' : 'light',
                    backgroundColor: colors.background,
                    autoHide: true,
                  });
                  // Reset email change state on error
                  setUserData({ ...userData, email: initialUserData.email });
                  setEmailChanged(false);
                }
              } else {
                // Reset email change state if no OTP entered
                setUserData({ ...userData, email: initialUserData.email });
                setEmailChanged(false);
              }
            },
          },
        ],
        'plain-text'
      );
    } catch (error) {
      console.log(error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to send OTP. Please try again.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
      // Reset email change state on error
      setUserData({ ...userData, email: initialUserData.email });
      setEmailChanged(false);
    } finally {
      Keyboard.dismiss();
    }
  };

  const handlePhoneSave = (value: string) => {
    handleSubmit({ ...userData, phoneNumber: value });
    setUserData({ ...userData, phoneNumber: value });
    setPhoneChanged(true);
    // In a real app, you would trigger phone verification here
  };

  const handlePasswordSave = async (oldPassword: string, newPassword: string) => {
    setIsPasswordLoading(true);
    try {
      // Send OTP to verify password change
      const response = await sendEmailOtp(userData.email);

      const currentUserID = response.body.userId; // Store the userID from response

      // Prompt user to enter OTP
      Alert.prompt(
        'Verify Password Change',
        'Enter the OTP sent to your email address.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => {
              setIsPasswordLoading(false);
            },
          },
          {
            text: 'Verify',
            onPress: async (otp) => {
              if (otp) {
                try {
                  // Verify OTP

                  await AuthService.verifyEmail(otp, currentUserID);

                  Toast.show({
                    type: 'success',
                    text1: 'Verified',
                    text2: 'Your email has been verified.',
                    position: 'bottom',
                    theme: isDark ? 'dark' : 'light',
                    backgroundColor: colors.background,
                    autoHide: true,
                  });

                  // Update password after OTP verification
                  await AuthService.changePassword(userData.email, oldPassword, newPassword);

                  Toast.show({
                    type: 'success',
                    text1: 'Password Updated',
                    text2: 'Your password has been changed successfully.',
                    position: 'bottom',
                    theme: isDark ? 'dark' : 'light',
                    backgroundColor: colors.background,
                    autoHide: true,
                  });

                  // Show logout notification
                  setTimeout(() => {
                    Toast.show({
                      type: 'info',
                      text1: 'Security Notice',
                      text2:
                        'For security, you will be logged out. Please log in with your new password.',
                      position: 'bottom',
                      theme: isDark ? 'dark' : 'light',
                      backgroundColor: colors.background,
                      autoHide: false,
                    });
                  }, 2000);

                  // Log out user after a brief delay
                  setTimeout(() => {
                    AuthService.logout();
                  }, 4000);
                } catch (error) {
                  Toast.show({
                    type: 'error',
                    text1: 'Error',
                    text2: error.message,
                    position: 'bottom',
                    theme: isDark ? 'dark' : 'light',
                    backgroundColor: colors.background,
                    autoHide: true,
                  });
                } finally {
                  setIsPasswordLoading(false);
                }
              } else {
                setIsPasswordLoading(false);
              }
            },
          },
        ],
        'plain-text'
      );
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to send OTP. Please try again.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
      setIsPasswordLoading(false);
    }
  };

  const handleBioSave = (value: string) => {
    handleSubmit({ ...userData, bio: value });
    setUserData({ ...userData, bio: value });
  };

  const handlePhotoSave = (uri: string) => {
    setUserData({ ...userData, profileImage: uri });
  };

  const handleInterestsSave = (selectedItems: string[]) => {
    handleSubmit({ ...userData, interests: selectedItems });
    setUserData({ ...userData, interests: selectedItems });
  };

  const handleEventPrefsSave = (selectedItems: string[]) => {
    handleSubmit({ ...userData, eventPreferences: selectedItems });
    setUserData({ ...userData, eventPreferences: selectedItems });
  };

  const handleGenderSave = (value: string) => {
    handleSubmit({ ...userData, gender: value });
    setUserData({ ...userData, gender: value });
  };

  const handleSubmit = async (user: any) => {
    const payload = {
      userId: user.id,
      username: user.username,
      phoneNumber: user.phoneNumber,
      email: user.email,
      fullName: user.fullName,
      gender: user.gender,
      interests: user.interests,
      eventPreferences: user.eventPreferences,
      upFor: user.upFor,
      bio: user.bio,
      visibility: user.visibility,
    };

    setIsSubmitting(true);
    try {
      console.log(payload);
      const profileData = await UserService.updateProfile(payload);
      (UserStore.getState() as { setUser: (data: any) => void }).setUser(profileData.body);

      Toast.show({
        type: 'success',
        text1: 'Profile Update',
        text2: 'Successfully updated profile',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } catch (error: any) {
      console.log(error);
      Toast.show({
        type: 'error',
        text1: 'Failed to update profile',
        text2: error.message,
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const verifyContact = (type: 'email' | 'phone') => {
    if (type === 'email') {
      Alert.prompt(
        'Verify Email',
        'Enter the OTP sent to your email address.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Verify',
            onPress: async (otp) => {
              if (otp) {
                await verifyEmailOtp(userData.email, otp);
              }
            },
          },
        ],
        'plain-text'
      );
    } else {
      Alert.alert(`Verify Phone`, `A verification code has been sent to your phone number.`, [
        {
          text: 'OK',
          onPress: () => {
            setPhoneChanged(false);
            Alert.alert('Verified', 'Your phone has been verified.');
          },
        },
      ]);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      style={{ flex: 1 }}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View className={`flex-1 `} style={{ backgroundColor: colors.background }}>
          <StatusBar style={isDark ? 'light' : 'dark'} />

          {/* Header */}
          <View className="flex-row items-center justify-between px-4 pb-2 pt-14">
            <TouchableOpacity onPress={() => router.back()}>
              <Ionicons name="arrow-back" size={24} color={isDark ? '#fff' : '#000'} />
            </TouchableOpacity>
            <Text className={`text-xl font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
              Edit Profile
            </Text>

            <Text className="font-bold text-lg text-violet-600"></Text>
          </View>

          {/* Content */}
          <ScrollView className="flex-1 px-4" showsVerticalScrollIndicator={false}>
            {/* Profile Photo */}
            <View className="mb-2 items-center py-6">
              <View className="relative">
                <Image source={{ uri: userData.profileImage }} className="h-24 w-24 rounded-full" />
                <TouchableOpacity
                  className="absolute bottom-0 right-0 rounded-full border-2 border-white bg-violet-600 p-2"
                  onPress={() => photoSheetRef.current?.present()}>
                  <MaterialCommunityIcons name="camera" size={18} color="#fff" />
                </TouchableOpacity>
              </View>
            </View>

            {/* Profile Details */}
            <View className={`mb-6 rounded-lg`} style={{ backgroundColor: colors.background }}>
              <TouchableOpacity
                className={`flex-row items-center justify-between border-b py-4 `}
                style={{ borderBottomColor: colors.grey5 }}
                onPress={() => nameSheetRef.current?.present()}>
                <View>
                  <Text
                    className={`font-bold text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    Full Name
                  </Text>
                  <Text className={`mt-1 text-base ${isDark ? 'text-white' : 'text-black'}`}>
                    {userData.fullName}
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={isDark ? '#bbb' : '#666'} />
              </TouchableOpacity>

              {/*   <TouchableOpacity
                className={`flex-row items-center justify-between border-b py-4 `}
                style={{ borderBottomColor: colors.grey5 }}
                onPress={() => usernameSheetRef.current?.present()}>
                <View>
                  <Text
                    className={`font-bold text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    Username
                  </Text>
                  <Text className={`mt-1 text-base ${isDark ? 'text-white' : 'text-black'}`}>
                    @{userData.username}
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={isDark ? '#bbb' : '#666'} />
              </TouchableOpacity> */}

              <TouchableOpacity
                className={`flex-row items-center justify-between border-b py-4 `}
                style={{ borderBottomColor: colors.grey5 }}
                onPress={() => emailSheetRef.current?.present()}>
                <View className="mr-2 flex-1">
                  <Text
                    className={`font-bold text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    Email
                  </Text>
                  <Text className={`mt-1 text-base ${isDark ? 'text-white' : 'text-black'}`}>
                    {userData.email}
                    {emailChanged && <Text className="text-yellow-500"> (Unverified)</Text>}
                  </Text>
                </View>
                {emailChanged ? (
                  <TouchableOpacity
                    className="rounded-full bg-violet-600 px-3 py-1"
                    onPress={() => verifyContact('email')}>
                    <Text className="font-bold text-base text-white">Verify</Text>
                  </TouchableOpacity>
                ) : (
                  <Ionicons name="chevron-forward" size={20} color={isDark ? '#bbb' : '#666'} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                className={`flex-row items-center justify-between border-b py-4 `}
                style={{ borderBottomColor: colors.grey5 }}
                onPress={() => phoneSheetRef.current?.present()}>
                <View className="mr-2 flex-1">
                  <Text
                    className={`font-bold text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    Phone Number
                  </Text>
                  <Text className={`mt-1 text-base ${isDark ? 'text-white' : 'text-black'}`}>
                    {userData.phoneNumber}
                    {phoneChanged && <Text className="text-yellow-500"> (Unverified)</Text>}
                  </Text>
                </View>
                {phoneChanged ? (
                  <TouchableOpacity
                    className="rounded-full bg-violet-600 px-3 py-1"
                    onPress={() => verifyContact('phone')}>
                    <Text className="font-bold text-base text-white">Verify</Text>
                  </TouchableOpacity>
                ) : (
                  <Ionicons name="chevron-forward" size={20} color={isDark ? '#bbb' : '#666'} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                className={`flex-row items-center justify-between border-b py-4 `}
                style={{ borderBottomColor: colors.grey5 }}
                onPress={() => passwordSheetRef.current?.present()}>
                <View>
                  <Text
                    className={`font-bold text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    Password
                  </Text>
                  <Text className={`mt-1 text-base ${isDark ? 'text-white' : 'text-black'}`}>
                    ••••••••••
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={isDark ? '#bbb' : '#666'} />
              </TouchableOpacity>

              <TouchableOpacity
                className={`flex-row items-center justify-between border-b py-4 `}
                style={{ borderBottomColor: colors.grey5 }}
                onPress={() => genderSheetRef.current?.present()}>
                <View>
                  <Text
                    className={`font-bold text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    Gender
                  </Text>
                  <Text
                    className={`mt-1 text-base capitalize ${isDark ? 'text-white' : 'text-black'}`}>
                    {userData.gender}
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={isDark ? '#bbb' : '#666'} />
              </TouchableOpacity>

              <TouchableOpacity
                className={`flex-row items-center justify-between py-4`}
                onPress={() => bioSheetRef.current?.present()}>
                <View className="mr-2 flex-1">
                  <Text
                    className={`font-bold text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    Bio
                  </Text>
                  <Text
                    className={`mt-1 text-base ${isDark ? 'text-white' : 'text-black'}`}
                    numberOfLines={2}>
                    {userData.bio}
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={isDark ? '#bbb' : '#666'} />
              </TouchableOpacity>
            </View>

            {/* Preferences Section */}
            <Text className={`mb-3 text-lg font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
              Preferences
            </Text>

            <View className={`mb-8 rounded-lg `}>
              <TouchableOpacity
                className={`flex-row items-center justify-between border-b py-4 `}
                style={{ borderBottomColor: colors.grey5 }}
                onPress={() => interestsSheetRef.current?.present()}>
                <View className="mr-2 flex-1">
                  <Text
                    className={`font-bold text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    Interests
                  </Text>
                  <Text
                    className={`mt-1 text-base ${isDark ? 'text-white' : 'text-black'}`}
                    numberOfLines={1}>
                    {userData.interests.join(', ')}
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={isDark ? '#bbb' : '#666'} />
              </TouchableOpacity>

              <TouchableOpacity
                className="flex-row items-center justify-between py-4"
                onPress={() => eventPrefsSheetRef.current?.present()}>
                <View className="mr-2 flex-1">
                  <Text
                    className={`font-bold text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    Event Preferences
                  </Text>
                  <Text
                    className={`mt-1 text-base ${isDark ? 'text-white' : 'text-black'}`}
                    numberOfLines={1}>
                    {userData.eventPreferences.join(', ')}
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color={isDark ? '#bbb' : '#666'} />
              </TouchableOpacity>
            </View>
          </ScrollView>

          {/* Bottom Sheets */}

          {showHeavyUI && (
            <Suspense fallback={<Text>Loading...</Text>}>
              <Suspense fallback={<></>}>
                <EditProfileBottomSheet
                  ref={nameSheetRef}
                  title="Edit Full Name"
                  currentValue={userData.fullName}
                  fieldName="Full Name"
                  onSave={handleFullNameSave}
                />

                <EditProfileBottomSheet
                  ref={usernameSheetRef}
                  title="Edit Username"
                  currentValue={userData.username}
                  fieldName="Username"
                  onSave={handleUsernameSave}
                />

                <EditProfileBottomSheet
                  ref={emailSheetRef}
                  title="Edit Email"
                  currentValue={userData.email}
                  fieldName="Email Address"
                  isEmail={true}
                  keyboardType="email-address"
                  onSave={handleEmailSave}
                />

                <EditProfileBottomSheet
                  ref={phoneSheetRef}
                  title="Edit Phone Number"
                  currentValue={userData.phoneNumber}
                  fieldName="Phone Number"
                  isPhone={true}
                  keyboardType="phone-pad"
                  onSave={handlePhoneSave}
                />

                <ChangePasswordBottomSheet
                  ref={passwordSheetRef}
                  onSave={handlePasswordSave}
                  isLoading={isPasswordLoading}
                />

                <EditProfileBottomSheet
                  ref={bioSheetRef}
                  title="Edit Bio"
                  currentValue={userData.bio}
                  fieldName="Bio"
                  multiline={true}
                  maxLength={150}
                  onSave={handleBioSave}
                />

                <PhotoUploadBottomSheet
                  ref={photoSheetRef}
                  currentPhoto={userData.profileImage}
                  onPhotoSelected={handlePhotoSave}
                />

                <MultiSelectBottomSheet
                  ref={interestsSheetRef}
                  title="Select Interests"
                  options={allInterests}
                  selectedItems={userData.interests}
                  onSave={handleInterestsSave}
                />

                <MultiSelectBottomSheet
                  ref={eventPrefsSheetRef}
                  title="Event Preferences"
                  options={allEventPreferences}
                  selectedItems={userData.eventPreferences}
                  onSave={handleEventPrefsSave}
                />

                <GenderSelectBottomSheet
                  ref={genderSheetRef}
                  currentValue={userData.gender}
                  onSave={handleGenderSave}
                />
              </Suspense>
            </Suspense>
          )}
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}
