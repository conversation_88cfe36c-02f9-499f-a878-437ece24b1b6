import { MarkerView } from '@rnmapbox/maps';
import { OnPressEvent } from '@rnmapbox/maps/lib/typescript/src/types/OnPressEvent';
import { View, Image, TouchableOpacity, Text } from 'react-native';

import { foodPin, businessPin, defaultPin } from '~/assets/Pins';
import { useEvent } from '~/providers/MapProvider';
import { EventType } from '~/types';

function EventMarkersPage() {
  const { setSelectedEvent, filteredEvents, isLoadingEvents } = useEvent();

  // Don't render markers while events are loading
  if (isLoadingEvents || filteredEvents.length === 0) {
    return null;
  }

  const getMarkerImage = (category: string) => {
    switch (category) {
      case 'Food':
        return foodPin;
      case 'Business':
        return businessPin;
      default:
        return defaultPin;
    }
  };

  const getMarkerEmoji = (category: string) => {
    switch (category) {
      case 'EDUCATION':
        return '📚';
      case 'BUSINESS':
        return '💼';
      case 'LEISURE':
        return '🎉';
      case 'ENTERTAINMENT':
        return '🍿';
      default:
        return '📍';
    }
  };

  const onMarkerPress = (event: EventType) => {
    setSelectedEvent(event);
  };

  return (
    <>
      {filteredEvents.map((event) => (
        <MarkerView
          key={event.id}
          coordinate={[event.locationData.coordinates[0], event.locationData.coordinates[1]]}
          anchor={{ x: 0.5, y: 1 }}
          allowOverlap
          allowOverlapWithPuck>
          <TouchableOpacity onPress={() => onMarkerPress(event)}>
            <View style={{ position: 'relative', width: 50, height: 50 }}>
              <Image
                source={getMarkerImage(event.eventType)}
                style={{ width: 50, height: 50 }}
                resizeMode="contain"
              />
              <Text
                style={{
                  position: 'absolute',
                  top: 9,
                  left: 0,
                  right: 0,
                  textAlign: 'center',
                  fontSize: 18,
                }}>
                {getMarkerEmoji(event.eventType)}
              </Text>
            </View>
          </TouchableOpacity>
        </MarkerView>
      ))}
    </>
  );
}

export default function EventMarkers() {
  return <EventMarkersPage />;
}
