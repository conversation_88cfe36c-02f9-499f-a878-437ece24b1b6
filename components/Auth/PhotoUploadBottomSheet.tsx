import React, { useRef, forwardRef, useImperativeHandle } from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet, Alert } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import BottomSheet, {
  BottomSheetView,
  BottomSheetBackdrop,
  BottomSheetModal,
} from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '../RenderBackdrop';
import * as ImagePicker from 'expo-image-picker';

export interface PhotoBottomSheetProps {
  currentPhoto: string;
  onPhotoSelected: (uri: string) => void;
}

export interface PhotoBottomSheetHandle {
  present: () => void;
  dismiss: () => void;
}

const PhotoUploadBottomSheet = forwardRef<PhotoBottomSheetHandle, PhotoBottomSheetProps>(
  ({ currentPhoto, onPhotoSelected }, ref) => {
    const bottomSheetModalRef = useRef<BottomSheetModal>(null);
    const { colorScheme, colors } = useColorScheme();
    const isDark = colorScheme === 'dark';

    useImperativeHandle(ref, () => ({
      present: () => {
        bottomSheetModalRef.current?.expand();
      },
      dismiss: () => bottomSheetModalRef.current?.close(),
    }));

    const takePhoto = async () => {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission required', 'Camera permission is required to take photos');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.7,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        onPhotoSelected(result.assets[0].uri);
        bottomSheetModalRef.current?.close();
      }
    };

    const chooseFromLibrary = async () => {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission required', 'Gallery permission is required to select photos');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.7,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        onPhotoSelected(result.assets[0].uri);
        bottomSheetModalRef.current?.close();
      }
    };

    const removePhoto = () => {
      Alert.alert('Remove Profile Photo', 'Are you sure you want to remove your profile photo?', [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            // Use default avatar or placeholder
            onPhotoSelected(
              'https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp&f=y'
            );
            bottomSheetModalRef.current?.close();
          },
        },
      ]);
    };

    return (
      <BottomSheet
        ref={bottomSheetModalRef}
        index={-1}
        snapPoints={['60%']}
        backdropComponent={RenderBackdrop}
        backgroundStyle={{
          backgroundColor: colors.background,
          borderTopLeftRadius: 24,
          borderTopRightRadius: 24,
        }}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#555' : '#999',
          width: 40,
        }}>
        <BottomSheetView className="flex-1 px-5 pt-2">
          <View className="flex-row items-center justify-between mb-5">
            <Text className={`text-xl font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
              Profile Photo
            </Text>
            <TouchableOpacity className="p-2" onPress={() => bottomSheetModalRef.current?.close()}>
              <Ionicons name="close" size={24} color={isDark ? '#fff' : '#000'} />
            </TouchableOpacity>
          </View>

          <View className="flex-row justify-center mb-6">
            <Image source={{ uri: currentPhoto }} className="w-20 h-20 rounded-full" />
          </View>

          <View className="flex gap-3">
            <TouchableOpacity
              onPress={takePhoto}
              className={`h-14 flex-row items-center rounded-lg px-4 py-3 `}
              style={{ backgroundColor: colors.grey5 }}>
              <Ionicons
                name="camera"
                size={22}
                color={isDark ? '#fff' : '#000'}
                style={{ marginRight: 12 }}
              />
              <Text className={`font-medium ${isDark ? 'text-white' : 'text-black'}`}>
                Take Photo
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={chooseFromLibrary}
              className={`h-14 flex-row items-center rounded-lg px-4 py-3`}
              style={{ backgroundColor: colors.grey5 }}>
              <MaterialCommunityIcons
                name="image"
                size={22}
                color={isDark ? '#fff' : '#000'}
                style={{ marginRight: 12 }}
              />
              <Text className={`font-medium ${isDark ? 'text-white' : 'text-black'}`}>
                Choose from Library
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={removePhoto}
              className="flex-row items-center px-4 py-3 bg-red-500 rounded-lg h-14">
              <Ionicons name="trash" size={22} color="#fff" style={{ marginRight: 12 }} />
              <Text className="font-medium text-white">Remove Current Photo</Text>
            </TouchableOpacity>
          </View>
        </BottomSheetView>
      </BottomSheet>
    );
  }
);

export default PhotoUploadBottomSheet;
