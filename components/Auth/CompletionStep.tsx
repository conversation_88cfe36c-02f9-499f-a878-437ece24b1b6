import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { Button, ButtonText } from '@/components/ui/button';
import { FontAwesome5 } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import * as Location from 'expo-location';
import * as ImagePicker from 'expo-image-picker';
import { AuthStore } from '~/store/store';

interface FeatureItemProps {
  icon: string;
  title: string;
  description: string;
  isDark: boolean;
  color: string;
  titleColor: string;
}

const FeatureItem = ({ icon, title, description, isDark, color, titleColor }: FeatureItemProps) => (
  <View className="flex-row items-start mb-4">
    <View className={`${'bg-violet-900'} mr-3 h-10 w-10 items-center justify-center rounded-full`}>
      <FontAwesome5 name={icon} size={16} color="white" />
    </View>
    <View className="flex-1">
      <Text className={`font-bold text-base`} style={{ color: titleColor }}>
        {title}
      </Text>
      <Text className={`text-sm `} style={{ color: color }}>
        {description}
      </Text>
    </View>
  </View>
);

export default function CompletionStep() {
  const { colorScheme, colors } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const isLoggedIn = AuthStore((state) => state.isLoggedIn);

  const [locationPermission, setLocationPermission] = useState<'granted' | 'denied' | 'pending'>(
    'pending'
  );
  const [mediaPermission, setMediaPermission] = useState<'granted' | 'denied' | 'pending'>(
    'pending'
  );

  // Check permissions when component mounts
  useEffect(() => {
    checkPermissions();
  }, []);

  const checkPermissions = async () => {
    try {
      // Check location permission
      const locationStatus = await Location.getForegroundPermissionsAsync();
      setLocationPermission(locationStatus.granted ? 'granted' : 'denied');

      // Check media library permission using ImagePicker
      const mediaStatus = await ImagePicker.getMediaLibraryPermissionsAsync();
      setMediaPermission(mediaStatus.granted ? 'granted' : 'denied');
    } catch (error) {
      console.error('Error checking permissions:', error);
    }
  };

  const handleGetStarted = async () => {
    router.replace('/Auth/login');
    /*  if (!isLoggedIn) {
      router.replace('/Auth/login');
    } else {
      await checkPermissions();
      if (locationPermission === 'granted' && mediaPermission === 'granted') {
        router.replace('/Auth/login');
      } else {
        router.replace('/Auth/permissions');
      }
    } */
  };

  const features = [
    {
      icon: 'map-marked-alt',
      title: 'Browse Events',
      description: 'Discover events happening around you on the interactive map',
    },
    {
      icon: 'users',
      title: 'Find People',
      description: 'Connect with people who share your interests',
    },
    {
      icon: 'calendar-plus',
      title: 'Create Events',
      description: 'Host your own events and invite others to join',
    },
    {
      icon: 'ticket-alt',
      title: 'Buy Tickets',
      description: 'Purchase tickets to events directly through the app',
    },
  ];

  return (
    <ScrollView className="flex-1 px-5 py-6 ">
      <View className="items-center mb-6">
        <View className="items-center justify-center w-16 h-16 mb-3 bg-green-100 rounded-full">
          <FontAwesome5 name="check" size={30} color="#10b981" />
        </View>
        <Text className="mb-1 text-xl font-bold" style={{ color: colors.foreground }}>
          You're all set!
        </Text>
        <Text
          className="font-sans text-base text-center text-gray-500"
          style={{ color: colors.grey }}>
          Your account is ready to use
        </Text>
      </View>

      <View className="mt-4 mb-6">
        {features.map((feature, index) => (
          <FeatureItem
            key={index}
            icon={feature.icon}
            title={feature.title}
            description={feature.description}
            isDark={isDark}
            color={colors.grey}
            titleColor={colors.foreground}
          />
        ))}
      </View>

      <Button
        className={`mb-10 h-12 rounded-lg ${isDark ? 'bg-violet-700' : 'bg-violet-600'}`}
        onPress={handleGetStarted}>
        <ButtonText className="text-white">Get Started</ButtonText>
      </Button>
    </ScrollView>
  );
}
