import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { UserData } from '~/app/Auth/signup';
import { Button, ButtonText } from '@/components/ui/button';
import { FontAwesome5 } from '@expo/vector-icons';
import {
  CodeField,
  Cursor,
  useBlurOnFulfill,
  useClearByFocusCell,
} from 'react-native-confirmation-code-field';
import { useRouter } from 'expo-router';
import { Toast } from 'toastify-react-native';
import { AuthService } from '~/services/AuthService';
import { AuthStore } from '~/store/store';
import { UserService } from '~/services/UserService';

const CELL_COUNT = 4;

interface EmailVerificationStepProps {
  userData: UserData;
  updateUserData: (data: Partial<UserData>) => void;
  onNext: () => void;
  resendCode: () => void;
  onBack: () => void;
}

export default function EmailVerificationStep({
  userData,
  updateUserData,
  onNext,
  resendCode,
  onBack,
}: EmailVerificationStepProps) {
  const { colorScheme, colors } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [value, setValue] = useState('');
  const [timer, setTimer] = useState(30);
  const [isResending, setIsResending] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [verificationMethod, setVerificationMethod] = useState<'email' | 'phone'>('email');

  const ref = useBlurOnFulfill({ value, cellCount: CELL_COUNT });
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });
  const initialRegistration = AuthStore((state) => state.initialRegistration);

  // Timer for resend code
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (timer > 0 && isResending) {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
    } else if (timer === 0) {
      setIsResending(false);
      setTimer(30);
    }

    return () => clearInterval(interval);
  }, [timer, isResending]);

  const handleResendCode = async () => {
    setIsResending(true);
    try {
      await resendCode();
      Toast.show({
        type: 'success',
        text1: 'Code Resent Successfully',
        text2: 'Please check your email or phone for the new code.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error Resending Code',
        text2: error.message,
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setIsResending(false);
    }
  };

  const handleVerify = async () => {
    if (value.length !== CELL_COUNT) {
      Toast.show({
        type: 'error',
        text1: 'Invalid Code',
        text2: 'Please enter the complete verification code.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
      return;
    }

    setVerifying(true);
    try {
      const response = await UserService.registerUser({
        fullName: userData.fullName,
        email: userData.email,
        phoneNumber: userData.phoneNumber,
        password: userData.password,
        verificationCode: value,
        userId: initialRegistration.userId,
        termsAccepted: true,
      });
      console.log(response);
      Toast.show({
        type: 'success',
        text1: 'Verification Successful',
        text2: 'Your email or phone has been verified.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
      await onNext();
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Verification Failed',
        text2: error.message,
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setVerifying(false);
    }
  };

  const handleChangeEmail = () => {
    Alert.alert('Change Email', 'Would you like to go back and change your email address?', [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Yes', onPress: onBack },
    ]);
  };

  const handleChangePhone = () => {
    Alert.alert('Change Phone Number', 'Would you like to go back and change your phone number?', [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Yes', onPress: onBack },
    ]);
  };

  const toggleVerificationMethod = () => {
    setVerificationMethod((prevMethod) => (prevMethod === 'email' ? 'phone' : 'email'));
    setValue(''); // Clear the code field when switching methods
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1 px-6">
      <Text className="mt-6 text-2xl font-bold " style={{ color: colors.foreground }}>
        Verify your {verificationMethod === 'email' ? 'email' : 'phone'}
      </Text>

      <Text className="mt-2 mb-2 text-base " style={{ color: colors.grey }}>
        We've sent a verification code to{'\n'}
        <Text className="font-bold" style={{ color: colors.foreground }}>
          {verificationMethod === 'email' ? userData.email : userData.phoneNumber}
        </Text>
      </Text>

      <View className="flex-row items-center gap-5 my-2 mb-5">
        {verificationMethod === 'email' ? (
          <TouchableOpacity onPress={handleChangeEmail}>
            <Text className="font-sans underline" style={{ color: colors.primary }}>
              Change Email
            </Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity onPress={handleChangePhone}>
            <Text className="font-sans underline" style={{ color: colors.primary }}>
              Change Phone Number
            </Text>
          </TouchableOpacity>
        )}
        {userData.email && userData.phoneNumber && (
          <TouchableOpacity className="items-center" onPress={toggleVerificationMethod}>
            <Text className="font-sans underline" style={{ color: colors.primary }}>
              Use {verificationMethod === 'email' ? 'Phone' : 'Email'} instead
            </Text>
          </TouchableOpacity>
        )}
      </View>

      <CodeField
        ref={ref}
        {...props}
        value={value}
        onChangeText={setValue}
        cellCount={CELL_COUNT}
        rootStyle={{ marginTop: 20 }}
        keyboardType="number-pad"
        textContentType="oneTimeCode"
        renderCell={({ index, symbol, isFocused }) => (
          <View
            key={index}
            onLayout={getCellOnLayoutHandler(index)}
            className={`mx-2 h-16 w-16 items-center justify-center rounded-lg border-2 ${
              isFocused
                ? `border-violet-600`
                : isDark
                  ? 'border-gray-700 bg-gray-800'
                  : 'border-gray-300 bg-gray-100'
            }`}
            style={{ backgroundColor: colors.grey5 }}>
            <Text className="text-2xl font-medium" style={{ color: colors.foreground }}>
              {symbol || (isFocused ? <Cursor /> : null)}
            </Text>
          </View>
        )}
        containerStyle={{ justifyContent: 'center', marginBottom: 30 }}
      />

      <Button
        className={`mt-10 h-14 rounded-xl font-bold ${
          value.length === CELL_COUNT
            ? isDark
              ? 'bg-violet-700'
              : 'bg-violet-600'
            : isDark
              ? 'bg-gray-700'
              : 'bg-gray-300'
        }`}
        onPress={handleVerify}
        isDisabled={value.length !== CELL_COUNT || verifying}>
        <ButtonText className="text-white">{verifying ? 'Verifying...' : 'Verify'}</ButtonText>
      </Button>

      <View className="flex-row justify-center mt-8">
        <Text className="text-gray-500">Didn't receive a code? </Text>
        {isResending ? (
          <Text className="font-medium" style={{ color: colors.primary }}>
            Resend in {timer}s
          </Text>
        ) : (
          <TouchableOpacity onPress={handleResendCode}>
            <Text className="font-medium" style={{ color: colors.primary }}>
              Resend Code
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </KeyboardAvoidingView>
  );
}
