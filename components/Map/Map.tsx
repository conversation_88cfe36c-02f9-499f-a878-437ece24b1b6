import Mapbox, { Camera, LocationPuck, MapView } from '@rnmapbox/maps';
import React, { useState, useCallback, useMemo, memo } from 'react';

import EventMarkers from '../Event/EventMarkers';
import PeopleMarker from '../People/PeopleMarkers';

import { useColorScheme } from '~/lib/useColorScheme';
import { useEvent, useEventValue } from '~/providers/MapProvider';
import { MapTsTypeMap } from '~/types';

Mapbox.setAccessToken(
  'pk.eyJ1IjoiZW1hY2xpYW0iLCJhIjoiY2wxNHRpcGNiMGR1dzNla2FndWVpdmJxbyJ9.wnxGUxO6rO3CoGaKyuXbVA'
);

// Memoized marker components to prevent unnecessary rerenders
const MemoizedEventMarkers = memo(EventMarkers);
const MemoizedPeopleMarker = memo(PeopleMarker);

const Map: React.FC = (props: any, ref: any) => {
  // Use selective context values to prevent rerenders when other context values change
  const { isDarkColorScheme } = useColorScheme();
  const cameraRef = useEventValue('cameraRef');
  const followUserLocation = useEventValue('followUserLocation');
  const MapType = useEventValue('MapType');
  const setZoomLevel = useEventValue('setZoomLevel');

  // Memoize map style to prevent recalculation on every render
  const mapStyle = useMemo(
    () =>
      isDarkColorScheme ? 'mapbox://styles/mapbox/dark-v11' : 'mapbox://styles/mapbox/outdoors-v12',
    [isDarkColorScheme]
  );

  // Memoize camera changed handler to prevent creating a new function on every render
  const handleCameraChanged = useCallback(
    (e: any) => {
      if (e.properties.zoom) {
        setZoomLevel(e.properties.zoom);
      }
    },
    [setZoomLevel]
  );

  // Memoize the markers to prevent unnecessary rerenders
  const mapMarkers = useMemo(() => {
    return MapType === MapTsTypeMap.Events ? <MemoizedEventMarkers /> : <MemoizedPeopleMarker />;
  }, [MapType]);

  return (
    <MapView
      logoEnabled={false} // Removes Mapbox logo
      attributionEnabled={false} // Removes attribution icon
      scaleBarEnabled={false}
      rotateEnabled
      style={{ flex: 1 }}
      styleURL={mapStyle}
      onCameraChanged={handleCameraChanged}>
      <Camera
        followZoomLevel={followUserLocation ? 12 : undefined}
        followUserLocation={followUserLocation}
        animationDuration={100}
        ref={cameraRef}
      />
      <LocationPuck puckBearingEnabled puckBearing="heading" pulsing={{ isEnabled: true }} />
      {mapMarkers}
    </MapView>
  );
};

// Wrap the component with memo to prevent unnecessary rerenders
export default memo(Map);
